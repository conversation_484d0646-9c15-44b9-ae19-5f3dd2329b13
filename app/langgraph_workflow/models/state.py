import operator
from typing import Annotated, List, Optional

from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

from app.langgraph_workflow.models.workflowGraph import WorkflowGraph

class State(BaseModel):
    main_agent_messages: Annotated[List[AnyMessage], operator.add]
    planner_messages: Annotated[List[AnyMessage], operator.add]
    workflow_generation_messages: Annotated[List[AnyMessage], operator.add]
    plan: Optional[str] = None
    feedback: Optional[str] = None
    workflow_graph: WorkflowGraph
